#!/usr/bin/env python3
# run_mlflow_experiments.py
"""
MLflow实验运行脚本
自动运行MLP、DCNv1、DLRM模型的实验，并使用MLflow跟踪结果
集成了Windows和Unix系统的subprocess内存管理最佳实践
"""

import os
import sys
import subprocess
import logging
import time
import gc
import platform
import psutil
import json
import hashlib
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from mlflow_config import setup_mlflow_experiment, compare_models, get_best_run
from config import PROCESSED_DATA_DIR
from experiment_config import get_experiments

# 使用配置文件中的预处理数据目录
PRODUCTION_DATA_DIR = PROCESSED_DATA_DIR  # 使用config.py中配置的预处理数据目录
EXPERIMENT_STATE_FILE = "experiment_state.json"  # 实验状态文件，用于断点续传

# 检测操作系统
SYSTEM = platform.system().lower()
IS_WINDOWS = SYSTEM == 'windows'
IS_UNIX = SYSTEM in ['linux', 'darwin']

# 导入torch用于内存管理（延迟导入）
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 设置日志 - 修复日志输出问题
log_filename = f'mlflow_experiments_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'

# 清除现有的日志处理器
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

# 重新配置日志 - 确保实时写入
file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
file_handler.setLevel(logging.INFO)
file_handler.flush = lambda: file_handler.stream.flush()  # 强制实时刷新

console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[file_handler, console_handler],
    force=True  # 强制重新配置
)

# 确保日志立即写入磁盘
logging.getLogger().handlers[0].flush()

# 立即测试日志输出
print("=== MLflow Experiments Script Starting ===")
logging.info("=== MLflow Experiments Script Starting ===")
logging.info(f"Log file: {log_filename}")
print(f"Log file: {log_filename}")

def load_experiment_state():
    """
    加载实验状态，用于断点续传
    """
    if os.path.exists(EXPERIMENT_STATE_FILE):
        try:
            with open(EXPERIMENT_STATE_FILE, 'r', encoding='utf-8') as f:
                state = json.load(f)
            logging.info(f"Loaded experiment state: {len(state.get('completed_experiments', []))} completed experiments")
            return state
        except Exception as e:
            logging.warning(f"Failed to load experiment state: {e}")

    return {
        'completed_experiments': [],
        'failed_experiments': [],
        'start_time': datetime.now().isoformat(),
        'last_update': datetime.now().isoformat()
    }

def save_experiment_state(state):
    """
    保存实验状态到磁盘，确保实时更新
    """
    try:
        state['last_update'] = datetime.now().isoformat()

        # 先写入临时文件，然后原子性替换
        temp_file = EXPERIMENT_STATE_FILE + '.tmp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, indent=2, ensure_ascii=False)
            f.flush()  # 强制写入磁盘
            os.fsync(f.fileno())  # 确保数据写入磁盘

        # 原子性替换
        if os.path.exists(EXPERIMENT_STATE_FILE):
            os.replace(temp_file, EXPERIMENT_STATE_FILE)
        else:
            os.rename(temp_file, EXPERIMENT_STATE_FILE)

        logging.debug("Experiment state saved successfully")

    except Exception as e:
        logging.error(f"Failed to save experiment state: {e}")

def generate_experiment_id(model_type, learning_rate, epochs, batch_size):
    """
    生成实验的唯一ID，用于断点续传
    """
    experiment_str = f"{model_type}_{learning_rate}_{epochs}_{batch_size}"
    return hashlib.md5(experiment_str.encode()).hexdigest()[:12]

def is_experiment_completed(experiment_id, state):
    """
    检查实验是否已经完成
    """
    return experiment_id in [exp.get('experiment_id') for exp in state.get('completed_experiments', [])]

def force_cleanup_memory():
    """
    强制清理内存的工具函数
    """
    try:
        # 1. Python垃圾回收
        gc.collect()

        # 2. PyTorch GPU内存清理
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()

        # 3. 强制垃圾回收（多次）
        for _ in range(3):
            gc.collect()

        logging.debug("Memory cleanup completed")

    except Exception as e:
        logging.warning(f"Memory cleanup failed: {e}")

def log_memory_status(stage=""):
    """
    记录当前内存状态
    """
    try:
        cpu_memory = psutil.virtual_memory().percent
        logging.info(f"Memory status {stage}: CPU {cpu_memory:.1f}%")

        if TORCH_AVAILABLE and torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
            logging.info(f"Memory status {stage}: GPU {gpu_memory:.2f} GB")

    except Exception as e:
        logging.warning(f"Failed to log memory status: {e}")

def check_data_availability(data_dir=None):
    """
    检查预处理数据是否可用
    """
    if data_dir is None:
        data_dir = PRODUCTION_DATA_DIR  # 默认使用完整的Criteo数据集

    required_files = [
        'train_features.npy', 'train_labels.npy',
        'validation_features.npy', 'validation_labels.npy'
    ]

    for file in required_files:
        file_path = os.path.join(data_dir, file)
        if not os.path.exists(file_path):
            logging.error(f"Required data file not found: {file_path}")
            logging.error("Please run preprocessing first: python src/preprocess.py")
            logging.error("This will convert your S3 Parquet data to local NPY files for training")
            return False

    # 记录数据集大小信息
    try:
        import numpy as np
        train_features = np.load(os.path.join(data_dir, 'train_features.npy'), mmap_mode='r')
        val_features = np.load(os.path.join(data_dir, 'validation_features.npy'), mmap_mode='r')

        logging.info(f"Data availability check passed for {data_dir}")
        logging.info(f"Training samples: {train_features.shape[0]:,}")
        logging.info(f"Validation samples: {val_features.shape[0]:,}")
        logging.info(f"Feature dimensions: {train_features.shape[1]:,}")

        return True
    except Exception as e:
        logging.error(f"Error checking data dimensions: {e}")
        return False

def run_experiment_subprocess_windows(model_type, learning_rate, epochs, batch_size=None):
    """
    Windows系统下的subprocess实验调用

    Args:
        model_type: 模型类型 (mlp, dcnv1, dlrm, dcnv2)
        learning_rate: 学习率
        epochs: 训练轮数
        batch_size: 批次大小

    Returns:
        bool: 实验是否成功
    """
    # 1. 构建命令
    python_exe = sys.executable
    script_path = os.path.join('src', 'train.py')

    cmd = [
        python_exe,
        script_path,
        '--model_type', model_type,
        '--learning_rate', str(learning_rate),
        '--epochs', str(epochs),
        '--data_dir', PRODUCTION_DATA_DIR  # 使用完整的Criteo数据集
    ]

    if batch_size:
        cmd.extend(['--batch_size', str(batch_size)])

    # 2. 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = os.getcwd()  # 确保Python路径正确
    env['CUDA_VISIBLE_DEVICES'] = '0'  # 限制GPU使用

    # 3. Windows特定的subprocess配置
    startupinfo = None
    if os.name == 'nt':  # Windows
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE  # 隐藏窗口

    try:
        logging.info(f"Starting {model_type.upper()} experiment...")
        logging.info(f"Command: {' '.join(cmd)}")

        start_time = time.time()

        # 4. 执行subprocess
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600,  # 1小时超时
            cwd=os.getcwd(),  # 明确指定工作目录
            env=env,
            startupinfo=startupinfo,  # Windows特定配置
            shell=False,  # Windows上建议False
            check=False  # 不自动抛出异常
        )

        end_time = time.time()
        duration = end_time - start_time

        # 5. 处理结果
        if result.returncode == 0:
            logging.info(f"✅ {model_type.upper()} experiment completed successfully in {duration:.1f}s")
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)

            # 强制刷新日志到磁盘
            for handler in logging.getLogger().handlers:
                handler.flush()

            return True
        else:
            logging.error(f"❌ {model_type.upper()} experiment failed with return code {result.returncode}")
            if result.stderr:
                logging.error("STDERR:")
                logging.error(result.stderr)
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)

            # 强制刷新日志到磁盘
            for handler in logging.getLogger().handlers:
                handler.flush()

            return False

    except subprocess.TimeoutExpired:
        logging.error(f"❌ {model_type.upper()} experiment timed out after 1 hour")
        return False
    except FileNotFoundError:
        logging.error(f"❌ Python executable or script not found: {python_exe}, {script_path}")
        return False
    except Exception as e:
        logging.error(f"❌ Unexpected error running {model_type.upper()} experiment: {e}")
        return False

def run_experiment_subprocess_unix(model_type, learning_rate, epochs, batch_size=None):
    """
    Unix系统下的subprocess实验调用
    利用Unix的进程管理优势
    """
    # 1. 构建命令
    cmd = [
        sys.executable,
        'src/train.py',
        '--model_type', model_type,
        '--learning_rate', str(learning_rate),
        '--epochs', str(epochs),
        '--data_dir', PRODUCTION_DATA_DIR  # 使用完整的Criteo数据集
    ]

    if batch_size:
        cmd.extend(['--batch_size', str(batch_size)])

    # 2. 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = os.getcwd()
    env['CUDA_VISIBLE_DEVICES'] = '0'
    env['OMP_NUM_THREADS'] = '8'  # 限制OpenMP线程数

    try:
        logging.info(f"Starting {model_type.upper()} experiment...")
        logging.info(f"Command: {' '.join(cmd)}")

        start_time = time.time()

        # 4. 执行subprocess
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600,  # 1小时超时
            cwd=os.getcwd(),
            env=env,
            shell=False
        )

        end_time = time.time()
        duration = end_time - start_time

        # 5. 处理结果
        if result.returncode == 0:
            logging.info(f"✅ {model_type.upper()} experiment completed successfully in {duration:.1f}s")
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)

            # 强制刷新日志到磁盘
            for handler in logging.getLogger().handlers:
                handler.flush()

            return True
        else:
            logging.error(f"❌ {model_type.upper()} experiment failed with return code {result.returncode}")
            if result.stderr:
                logging.error("STDERR:")
                logging.error(result.stderr)
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)

            # 强制刷新日志到磁盘
            for handler in logging.getLogger().handlers:
                handler.flush()

            return False

    except subprocess.TimeoutExpired:
        logging.error(f"❌ {model_type.upper()} experiment timed out after 1 hour")
        return False
    except Exception as e:
        logging.error(f"❌ Unexpected error running {model_type.upper()} experiment: {e}")
        return False

def run_single_experiment(model_type, epochs=None, learning_rate=None, batch_size=None):
    """
    跨平台的单个实验运行函数
    根据操作系统自动选择最佳实现

    Args:
        model_type: 模型类型 ('mlp', 'dcnv1', 'dlrm', 'dcnv2')
        epochs: 训练轮数（可选）
        learning_rate: 学习率（可选）
        batch_size: 批次大小（可选）

    Returns:
        bool: 实验是否成功
    """
    logging.info(f"=== Starting {model_type.upper()} Experiment ===")
    logging.info(f"System: {SYSTEM}, Windows: {IS_WINDOWS}, Unix: {IS_UNIX}")

    # 记录初始内存状态
    log_memory_status("before experiment")

    # 强制清理内存
    force_cleanup_memory()

    try:
        # 根据操作系统选择实现
        if IS_WINDOWS:
            success = run_experiment_subprocess_windows(model_type, learning_rate, epochs, batch_size)
        elif IS_UNIX:
            success = run_experiment_subprocess_unix(model_type, learning_rate, epochs, batch_size)
        else:
            logging.warning(f"Unknown system: {SYSTEM}, using Windows implementation")
            success = run_experiment_subprocess_windows(model_type, learning_rate, epochs, batch_size)

        return success

    finally:
        # 实验后强制清理内存
        force_cleanup_memory()
        log_memory_status("after experiment")

def run_hyperparameter_experiments():
    """
    运行超参数优化实验
    """
    logging.info("=== Starting Hyperparameter Optimization Experiments ===")
    
    # 从experiment_config.py获取实验配置
    experiments = get_experiments('production')  # 使用生产级配置

    logging.info(f"Loaded {len(experiments)} experiments from experiment_config.py")
    logging.info("Experiment overview:")
    for i, exp in enumerate(experiments, 1):
        logging.info(f"  {i:2d}. {exp['model_type']:6s} | LR: {exp['learning_rate']:8.6f} | "
                    f"Epochs: {exp['epochs']:2d} | Batch: {exp['batch_size']:4d}")
    
    # 加载实验状态
    experiment_state = load_experiment_state()
    successful_experiments = len(experiment_state.get('completed_experiments', []))
    total_experiments = len(experiments)

    logging.info(f"Total experiments planned: {total_experiments}")
    logging.info(f"Previously completed experiments: {successful_experiments}")
    logging.info(f"Remaining experiments: {total_experiments - successful_experiments}")

    for i, exp in enumerate(experiments, 1):
        # 生成实验ID
        experiment_id = generate_experiment_id(
            exp['model_type'], exp['learning_rate'], exp['epochs'], exp.get('batch_size', 2048)
        )

        # 检查是否已经完成
        if is_experiment_completed(experiment_id, experiment_state):
            logging.info(f"⏭️  Experiment {i}/{total_experiments} already completed: {exp['description']}")
            continue

        logging.info(f"\n--- Experiment {i}/{total_experiments}: {exp['description']} ---")
        logging.info(f"Experiment ID: {experiment_id}")

        # 强制刷新日志
        for handler in logging.getLogger().handlers:
            handler.flush()

        start_time = time.time()
        success = run_single_experiment(
            model_type=exp['model_type'],
            learning_rate=exp['learning_rate'],
            epochs=exp['epochs'],
            batch_size=exp.get('batch_size')
        )
        end_time = time.time()

        # 记录实验结果到状态文件
        experiment_record = {
            'experiment_id': experiment_id,
            'model_type': exp['model_type'],
            'learning_rate': exp['learning_rate'],
            'epochs': exp['epochs'],
            'batch_size': exp.get('batch_size', 2048),
            'description': exp['description'],
            'success': success,
            'duration_seconds': end_time - start_time,
            'completion_time': datetime.now().isoformat()
        }

        if success:
            experiment_state['completed_experiments'].append(experiment_record)
            successful_experiments += 1
            logging.info(f"✅ Experiment {i} completed successfully in {end_time - start_time:.1f}s")
        else:
            experiment_state['failed_experiments'].append(experiment_record)
            logging.error(f"❌ Experiment {i} failed after {end_time - start_time:.1f}s")

        # 立即保存状态
        save_experiment_state(experiment_state)

        # 强制刷新日志
        for handler in logging.getLogger().handlers:
            handler.flush()

        # 实验间休息，让系统稳定
        if i < total_experiments:
            sleep_time = 10 if IS_WINDOWS else 8  # 增加休息时间，因为是完整实验
            logging.info(f"Waiting {sleep_time} seconds before next experiment...")
            time.sleep(sleep_time)

            # 额外的内存清理
            force_cleanup_memory()
    
    logging.info(f"\n=== Hyperparameter Experiments Summary ===")
    logging.info(f"Successful experiments: {successful_experiments}/{total_experiments}")
    success_rate = successful_experiments / total_experiments * 100
    logging.info(f"Success Rate: {success_rate:.1f}%")

    # 最终内存清理
    force_cleanup_memory()
    log_memory_status("final")

    # 最终状态保存
    save_experiment_state(experiment_state)

    # 显示详细的实验总结
    logging.info(f"\n=== Detailed Experiment Summary ===")
    logging.info(f"Total experiments planned: {total_experiments}")
    logging.info(f"Successfully completed: {successful_experiments}")
    logging.info(f"Failed experiments: {len(experiment_state.get('failed_experiments', []))}")

    if experiment_state.get('completed_experiments'):
        logging.info(f"\n=== Completed Experiments ===")
        for exp in experiment_state['completed_experiments']:
            logging.info(f"  ✅ {exp['model_type']} (LR: {exp['learning_rate']}, Epochs: {exp['epochs']}) - {exp['duration_seconds']:.1f}s")

    if experiment_state.get('failed_experiments'):
        logging.info(f"\n=== Failed Experiments ===")
        for exp in experiment_state['failed_experiments']:
            logging.info(f"  ❌ {exp['model_type']} (LR: {exp['learning_rate']}, Epochs: {exp['epochs']}) - {exp['duration_seconds']:.1f}s")

    return successful_experiments == total_experiments

def analyze_results():
    """
    分析实验结果
    """
    logging.info("=== Analyzing Experiment Results ===")
    
    try:
        # 比较所有模型
        comparison_df = compare_models()
        
        if comparison_df is not None and len(comparison_df) > 0:
            logging.info("Experiment Results Summary:")
            logging.info(f"Total runs: {len(comparison_df)}")
            
            # 显示前5个最佳结果
            if 'metrics.val_auc' in comparison_df.columns:
                top_runs = comparison_df.head(5)
                logging.info("\nTop 5 runs by validation AUC:")
                for idx, row in top_runs.iterrows():
                    model_type = row.get('params.model_type', 'Unknown')
                    val_auc = row.get('metrics.val_auc', 'N/A')
                    test_auc = row.get('metrics.test_auc', 'N/A')
                    lr = row.get('params.learning_rate', 'N/A')
                    logging.info(f"  {model_type}: Val AUC={val_auc:.4f}, Test AUC={test_auc:.4f}, LR={lr}")
            
            # 获取最佳运行
            best_run = get_best_run()
            if best_run is not None:
                logging.info(f"\nBest overall run:")
                logging.info(f"  Run ID: {best_run['run_id']}")
                logging.info(f"  Model: {best_run.get('params.model_type', 'Unknown')}")
                logging.info(f"  Val AUC: {best_run.get('metrics.val_auc', 'N/A'):.4f}")
                logging.info(f"  Test AUC: {best_run.get('metrics.test_auc', 'N/A'):.4f}")
                logging.info(f"  Learning Rate: {best_run.get('params.learning_rate', 'N/A')}")
        else:
            logging.warning("No experiment results found")
            
    except Exception as e:
        logging.error(f"Error analyzing results: {e}")

def main():
    """
    主函数
    """
    print("=== Main function started ===")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python executable: {sys.executable}")

    logging.info("=== MLflow Hyperparameter Optimization Experiments ===")
    logging.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"System: {SYSTEM} ({'Windows' if IS_WINDOWS else 'Unix'} optimizations)")
    logging.info(f"PyTorch available: {TORCH_AVAILABLE}")

    print("=== Basic info logged ===")
    print(f"System: {SYSTEM}")
    print(f"PyTorch available: {TORCH_AVAILABLE}")

    # 记录初始系统状态
    log_memory_status("initial")

    # 设置MLflow实验
    try:
        setup_mlflow_experiment()
        logging.info("MLflow experiment setup completed")
    except Exception as e:
        logging.error(f"Failed to setup MLflow experiment: {e}")
        return False
    
    # 检查数据可用性（使用完整Criteo数据集）
    if not check_data_availability(PRODUCTION_DATA_DIR):
        logging.error(f"Production data not available in {PRODUCTION_DATA_DIR}. Please ensure Criteo data is preprocessed.")
        return False
    
    # 运行超参数实验
    start_time = time.time()

    try:
        success = run_hyperparameter_experiments()
    except Exception as e:
        logging.error(f"Critical error during experiments: {e}")
        success = False
    finally:
        # 确保最终清理
        force_cleanup_memory()

    end_time = time.time()
    total_time = end_time - start_time
    logging.info(f"\nTotal experiment time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")

    # 分析结果
    try:
        analyze_results()
    except Exception as e:
        logging.error(f"Error during result analysis: {e}")

    # 最终内存状态
    log_memory_status("final")

    # 提供MLflow UI启动指令
    logging.info("\n=== Next Steps ===")
    logging.info("To view results in MLflow UI, run:")
    logging.info("  python start_mlflow_ui.py")
    logging.info("  # or directly: mlflow ui")
    logging.info("Then open http://localhost:5000 in your browser")

    logging.info("\nTo generate analysis reports, run:")
    logging.info("  python src/experiment_analysis.py")

    logging.info(f"\nExperiment state saved to: {EXPERIMENT_STATE_FILE}")
    logging.info(f"Log file saved to: {log_filename}")

    logging.info(f"\nExperiment completed: {'✅ SUCCESS' if success else '❌ FAILED'}")
    logging.info(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 强制最终刷新所有日志
    for handler in logging.getLogger().handlers:
        handler.flush()

    return success

if __name__ == '__main__':
    print("=== Script execution started ===")
    print(f"Script path: {__file__}")
    print(f"Working directory: {os.getcwd()}")

    try:
        success = main()
        print(f"=== Script completed with success: {success} ===")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"=== Script failed with exception: {e} ===")
        import traceback
        traceback.print_exc()
        sys.exit(1)
