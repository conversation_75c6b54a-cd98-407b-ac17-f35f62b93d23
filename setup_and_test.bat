@echo off
REM Windows批处理脚本 - 自动设置环境并运行测试

echo ========================================
echo 智能推荐系统 - Windows 自动设置脚本
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 1. 创建虚拟环境...
python -m venv recommendation_venv
if errorlevel 1 (
    echo 错误: 创建虚拟环境失败
    pause
    exit /b 1
)

echo 2. 激活虚拟环境...
call .\recommendation_venv\Scripts\activate.bat

echo 3. 安装依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 安装依赖失败
    pause
    exit /b 1
)

echo 4. 运行快速测试...
python quick_test.py
if errorlevel 1 (
    echo 错误: 快速测试失败
    pause
    exit /b 1
)

echo ========================================
echo 🎉 设置完成！系统已准备就绪
echo ========================================
echo.
echo 下一步操作：
echo 1. 运行完整测试: python scripts\run_complete_test.py
echo 2. 使用您的数据: python examples\setup_data_config.py
echo 3. 手动训练模型: python src\train.py --model_type mlp --epochs 10
echo.
pause
