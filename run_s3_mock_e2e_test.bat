echo ========================================
echo S3 Mock End-to-End Test for Windows
echo ========================================
echo.

REM 检查虚拟环境是否存在
if not exist "recommendation_venv\Scripts\activate.bat" (
    echo Error: Virtual environment not found!
    echo Please run setup_and_test.bat first to create the virtual environment.
    pause
    exit /b 1
)

echo Activating virtual environment...
call recommendation_venv\Scripts\activate.bat

echo.
echo Checking dependencies...
python -c "import moto, boto3, s3fs; print('✓ All S3 mock dependencies available')" 2>nul
if errorlevel 1 (
    echo Installing S3 mock dependencies...
    pip install moto boto3 s3fs
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Running Complete S3 Mock E2E Test...
echo ========================================
echo This will test the entire pipeline:
echo - Mock S3 data generation
echo - Data analysis from S3
echo - Data preprocessing from S3  
echo - Model training with S3 data
echo ========================================
echo.

python tests\test_s3_mock.py

if errorlevel 1 (
    echo.
    echo ❌ S3 Mock E2E Test FAILED!
    echo Please check the error messages above.
) else (
    echo.
    echo ✅ S3 Mock E2E Test PASSED!
    echo Your system is ready for cloud deployment!
)

echo.
pause
