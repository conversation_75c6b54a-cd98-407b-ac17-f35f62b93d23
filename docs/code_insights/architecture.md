# 系统架构文档

## 背景

本文档使用C4模型（Context、Container、Component、Code）描述推荐系统的架构设计，从宏观到微观逐层展示系统结构。

## C1 - 系统上下文图（Context）

### 系统边界

```plantuml
@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

Person(user, "数据科学家", "训练和优化推荐模型")
Person(devops, "运维工程师", "部署和监控系统")

System(recsys, "推荐系统训练平台", "大规模推荐模型训练和优化系统")

System_Ext(s3, "AWS S3", "对象存储服务")
System_Ext(mlflow, "MLflow", "实验管理平台")
System_Ext(gpu_cluster, "GPU集群", "CUDA计算资源")
System_Ext(inference, "推理服务", "模型部署平台")

Rel(user, recsys, "训练模型", "CLI/Python")
Rel(devops, recsys, "配置和监控", "配置文件/日志")
Rel(recsys, s3, "读写数据", "S3 API")
Rel(recsys, mlflow, "记录实验", "MLflow API")
Rel(recsys, gpu_cluster, "GPU训练", "CUDA")
Rel(recsys, inference, "导出模型", "模型文件")

@enduml
```

### 外部依赖
- **AWS S3**：存储训练数据和模型文件
- **MLflow**：跟踪实验参数和指标
- **GPU集群**：提供CUDA计算加速
- **推理服务**：部署训练好的模型

### Why - 设计动机
采用云原生架构设计，充分利用云服务的弹性和可扩展性。S3提供无限存储容量，GPU集群提供强大算力，MLflow统一管理实验，形成完整的机器学习工作流。

## C2 - 容器图（Container）

### 系统容器划分

```plantuml
@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

Person(user, "数据科学家")

System_Boundary(recsys, "推荐系统训练平台") {
    Container(cli, "命令行接口", "Python Scripts", "提供训练和数据处理入口")
    Container(data_processor, "数据处理引擎", "Python/Multiprocessing", "并行数据预处理")
    Container(training_engine, "训练引擎", "PyTorch", "模型训练和优化")
    Container(model_registry, "模型注册", "Python", "模型版本管理")
    Container(common_lib, "公共库", "Python", "共享工具和抽象")
    ContainerDb(local_cache, "本地缓存", "NumPy/Parquet", "处理后的特征数据")
}

System_Ext(s3, "AWS S3")
System_Ext(mlflow, "MLflow")

Rel(user, cli, "执行命令", "Shell")
Rel(cli, data_processor, "触发处理", "Function Call")
Rel(cli, training_engine, "启动训练", "Function Call")
Rel(data_processor, s3, "读取原始数据", "S3FS")
Rel(data_processor, local_cache, "保存特征", "NumPy")
Rel(training_engine, local_cache, "加载特征", "NumPy")
Rel(training_engine, mlflow, "记录指标", "API")
Rel(training_engine, model_registry, "保存模型", "PyTorch")
Rel(data_processor, common_lib, "使用", "Import")
Rel(training_engine, common_lib, "使用", "Import")

@enduml
```

### 技术栈说明
- **命令行接口**：Python脚本，提供统一入口
- **数据处理引擎**：基于multiprocessing的并行处理
- **训练引擎**：PyTorch深度学习框架
- **模型注册**：模型版本控制和管理
- **公共库**：抽象的I/O、特征、训练工具
- **本地缓存**：NumPy二进制格式存储

### Why - 设计动机
容器化设计实现了关注点分离：数据处理独立于训练，便于分别优化；公共库避免代码重复；本地缓存减少重复计算，提升迭代效率。

## C3 - 组件图（Component）

### 数据处理引擎组件

```mermaid
graph TB
    subgraph "数据处理引擎"
        PP[ParallelProcessor<br/>并行处理器]
        PRE[Preprocessor<br/>预处理器]
        DA[DataAnalyzer<br/>数据分析器]
        FM[FeatureManager<br/>特征管理器]
        S3U[S3Utils<br/>S3工具]
        
        PP --> PRE
        PP --> S3U
        PRE --> DA
        PRE --> FM
        DA --> FM
    end
    
    subgraph "公共库"
        IO[common.io<br/>I/O抽象]
        FEA[common.features<br/>特征工具]
        DC[common.data_checks<br/>数据验证]
        FR[common.feature_registry<br/>特征注册]
        
        S3U --> IO
        PRE --> FEA
        PRE --> DC
        FM --> FR
    end
```

### 训练引擎组件

```mermaid
graph TB
    subgraph "训练引擎"
        TL[TrainLossOptimized<br/>优化训练器]
        MB[ModelBuilder<br/>模型构建器]
        GM[GradientMonitor<br/>梯度监控]
        AI[AdaptiveInit<br/>自适应初始化]
        
        TL --> MB
        TL --> GM
        MB --> AI
    end
    
    subgraph "模型定义"
        MLP[MLP模型]
        DCN2[DCNv2模型]
        DCN1[DCNv1模型]
        DLRM[DLRM模型]
        
        MB --> MLP
        MB --> DCN2
        MB --> DCN1
        MB --> DLRM
    end
    
    subgraph "公共库"
        TR[common.training<br/>训练工具]
        ME[common.metrics<br/>评估指标]
        MO[common.models<br/>模型工厂]
        
        TL --> TR
        TL --> ME
        MB --> MO
    end
```

### Why - 设计动机
组件化设计提高了代码复用性和可测试性。每个组件职责单一，通过接口交互，便于独立开发和测试。公共库的抽象层统一了接口，降低了耦合度。

## C4 - 代码级设计（Code）

### 核心类设计

#### DCNv2模型类

```python
class DCNv2(nn.Module):
    """深度交叉网络V2实现"""
    
    def __init__(self, input_dim, deep_layer_sizes, cross_layer_num):
        # 深度网络：MLP结构
        self.deep_layers = self._build_deep_layers(input_dim, deep_layer_sizes)
        
        # 交叉网络：特征交叉
        self.cross_layers = self._build_cross_layers(input_dim, cross_layer_num)
        
        # 输出层：合并深度和交叉特征
        self.output_layer = nn.Linear(
            deep_layer_sizes[-1] + input_dim, 1
        )
    
    def forward(self, x):
        # 并行处理深度和交叉路径
        deep_out = self.deep_layers(x)
        cross_out = self.cross_layers(x)
        
        # 合并输出
        concat_out = torch.cat([deep_out, cross_out], dim=1)
        return self.output_layer(concat_out)
```

#### FeatureManager类

```python
class FeatureManager:
    """统一特征管理器"""
    
    def __init__(self, metadata_path):
        self.metadata = self._load_metadata(metadata_path)
        self.feature_indices = self._build_indices()
        self.feature_groups = self._organize_groups()
    
    def select_features(self, include_groups=None, exclude_groups=None):
        """基于组的特征选择"""
        selected_indices = []
        
        for group, indices in self.feature_groups.items():
            if include_groups and group not in include_groups:
                continue
            if exclude_groups and group in exclude_groups:
                continue
            selected_indices.extend(indices)
        
        return sorted(selected_indices)
    
    def get_feature_dim(self, selected_indices=None):
        """计算特征维度"""
        if selected_indices is None:
            return len(self.feature_indices)
        return len(selected_indices)
```

### 关键函数实现

#### 并行数据处理

```python
def process_files_parallel(file_list, num_workers, output_dir):
    """并行处理多个数据文件"""
    
    # 创建进程池
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        # 提交任务
        futures = []
        for file_path in file_list:
            future = executor.submit(
                process_single_file, 
                file_path, 
                output_dir
            )
            futures.append(future)
        
        # 收集结果
        results = []
        for future in tqdm(as_completed(futures), total=len(futures)):
            try:
                result = future.result(timeout=300)
                results.append(result)
            except Exception as e:
                logger.error(f"处理失败: {e}")
    
    return results
```

#### 特征转换

```python
def transform_features(df, feature_config):
    """DataFrame到NumPy数组的特征转换"""
    
    feature_arrays = []
    
    # 数值特征：直接提取
    for col in feature_config['numeric_columns']:
        values = df[col].fillna(0).values.reshape(-1, 1)
        feature_arrays.append(values)
    
    # 类别特征：独热编码
    for col in feature_config['categorical_columns']:
        encoded = pd.get_dummies(df[col], prefix=col)
        feature_arrays.append(encoded.values)
    
    # 数组特征：展开
    for col in feature_config['array_columns']:
        expanded = _expand_array_column(
            df[col], 
            feature_config['array_dimensions'][col]
        )
        feature_arrays.append(expanded)
    
    # 合并所有特征
    return np.concatenate(feature_arrays, axis=1).astype(np.float32)
```

### 配置管理

```python
class Config:
    """动态配置管理"""
    
    @classmethod
    def get_instance_config(cls):
        """根据硬件自动配置"""
        cpu_count = os.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        if cpu_count >= 88 and memory_gb >= 700:
            # r5.24xlarge配置
            return {
                'num_workers': 88,
                'chunk_size': 200000,
                'memory_limit_gb': 700,
                'batch_size': 32768
            }
        elif cpu_count >= 44 and memory_gb >= 350:
            # r5.12xlarge配置
            return {
                'num_workers': 44,
                'chunk_size': 100000,
                'memory_limit_gb': 350,
                'batch_size': 16384
            }
        else:
            # 保守配置
            return {
                'num_workers': max(4, cpu_count - 2),
                'chunk_size': 50000,
                'memory_limit_gb': memory_gb * 0.7,
                'batch_size': 8192
            }
```

### Why - 设计动机

#### 代码级设计原则
1. **单一职责**：每个类和函数只负责一个功能
2. **开闭原则**：对扩展开放，对修改关闭
3. **依赖倒置**：依赖抽象而非具体实现
4. **接口隔离**：客户端不应依赖不需要的接口

#### 性能优化策略
1. **并行处理**：充分利用多核CPU
2. **批处理**：减少I/O开销
3. **内存管理**：动态调整避免OOM
4. **缓存机制**：避免重复计算

## 架构演进

### 重构历程

```mermaid
graph LR
    V1[原始版本<br/>紧耦合] --> V2[模块化<br/>Step 1-3]
    V2 --> V3[公共库抽取<br/>Step 4-6]
    V3 --> V4[Shim移除<br/>Step 7]
    V4 --> V5[特征统一<br/>Step 9]
    V5 --> V6[生产就绪<br/>Step 10]
```

### 架构守卫

1. **S3访问控制**：只能通过common.io访问S3
2. **元数据基线**：SHA256验证防止破坏性变更
3. **测试覆盖**：12个合规性测试全覆盖
4. **模块边界**：强制模块间通过接口交互

## 部署架构

### 生产环境部署

```mermaid
graph TB
    subgraph "AWS VPC"
        subgraph "计算层"
            EC2[EC2实例<br/>r5.24xlarge]
            GPU[GPU实例<br/>p3.8xlarge]
        end
        
        subgraph "存储层"
            S3B[S3 Bucket<br/>训练数据]
            S3M[S3 Bucket<br/>模型存储]
        end
        
        subgraph "监控层"
            CW[CloudWatch<br/>日志监控]
            MLF[MLflow Server<br/>实验跟踪]
        end
        
        EC2 --> S3B
        GPU --> S3B
        EC2 --> S3M
        GPU --> S3M
        EC2 --> CW
        GPU --> CW
        EC2 --> MLF
        GPU --> MLF
    end
```

### 容器化部署（可选）

```dockerfile
# Dockerfile示例
FROM python:3.8-slim

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY src/ /app/src/
COPY scripts/ /app/scripts/

# 设置工作目录
WORKDIR /app

# 入口点
ENTRYPOINT ["python", "src/train_loss_optimized.py"]
```

## Why - 架构决策总结

### 核心权衡

1. **性能 vs 可维护性**
   - 选择：清晰的模块边界
   - 代价：轻微的性能开销
   - 收益：长期可维护性

2. **通用 vs 专用**
   - 选择：通用框架 + 专用优化
   - 实现：公共库提供通用能力，业务模块专门优化
   - 效果：既灵活又高效

3. **集中 vs 分布**
   - 选择：逻辑集中，执行分布
   - 方案：单一入口，多进程执行
   - 优势：易于管理和调试

### 架构优势

1. **可扩展性**：模块化设计便于添加新功能
2. **可测试性**：组件解耦便于单元测试
3. **可维护性**：清晰的代码组织和文档
4. **高性能**：多层次的优化策略
5. **生产就绪**：完善的监控和错误处理

---

*本架构文档展示了系统从宏观到微观的完整设计，体现了工程最佳实践和架构设计原则。*