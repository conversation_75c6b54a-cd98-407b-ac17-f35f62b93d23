#!/usr/bin/env python3
"""
实验状态检查脚本
用于查看当前实验进度和状态
"""

import json
import os
from datetime import datetime

EXPERIMENT_STATE_FILE = "experiment_state.json"

def load_experiment_state():
    """加载实验状态"""
    if os.path.exists(EXPERIMENT_STATE_FILE):
        try:
            with open(EXPERIMENT_STATE_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading experiment state: {e}")
            return None
    else:
        print(f"No experiment state file found: {EXPERIMENT_STATE_FILE}")
        return None

def display_experiment_status():
    """显示实验状态"""
    state = load_experiment_state()
    
    if state is None:
        print("No experiment state available.")
        return
    
    print("=" * 60)
    print("EXPERIMENT STATUS REPORT")
    print("=" * 60)
    
    print(f"Start Time: {state.get('start_time', 'Unknown')}")
    print(f"Last Update: {state.get('last_update', 'Unknown')}")
    
    completed = state.get('completed_experiments', [])
    failed = state.get('failed_experiments', [])
    
    print(f"\nCompleted Experiments: {len(completed)}")
    print(f"Failed Experiments: {len(failed)}")
    print(f"Total Experiments: {len(completed) + len(failed)}")
    
    if completed:
        print(f"\n{'='*20} COMPLETED EXPERIMENTS {'='*20}")
        total_time = 0
        for i, exp in enumerate(completed, 1):
            duration = exp.get('duration_seconds', 0)
            total_time += duration
            print(f"{i:2d}. {exp['model_type']:6s} | LR: {exp['learning_rate']:8.6f} | "
                  f"Epochs: {exp['epochs']:2d} | Batch: {exp['batch_size']:4d} | "
                  f"Time: {duration:6.1f}s | {exp.get('completion_time', 'Unknown')}")
        
        print(f"\nTotal training time: {total_time:.1f} seconds ({total_time/3600:.2f} hours)")
    
    if failed:
        print(f"\n{'='*20} FAILED EXPERIMENTS {'='*20}")
        for i, exp in enumerate(failed, 1):
            duration = exp.get('duration_seconds', 0)
            print(f"{i:2d}. {exp['model_type']:6s} | LR: {exp['learning_rate']:8.6f} | "
                  f"Epochs: {exp['epochs']:2d} | Batch: {exp['batch_size']:4d} | "
                  f"Time: {duration:6.1f}s | {exp.get('completion_time', 'Unknown')}")
    
    print("=" * 60)

def reset_experiment_state():
    """重置实验状态（谨慎使用）"""
    if os.path.exists(EXPERIMENT_STATE_FILE):
        backup_file = f"{EXPERIMENT_STATE_FILE}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.rename(EXPERIMENT_STATE_FILE, backup_file)
        print(f"Experiment state backed up to: {backup_file}")
        print("Experiment state reset. Next run will start from the beginning.")
    else:
        print("No experiment state file to reset.")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        confirm = input("Are you sure you want to reset experiment state? (yes/no): ")
        if confirm.lower() == 'yes':
            reset_experiment_state()
        else:
            print("Reset cancelled.")
    else:
        display_experiment_status()
        print("\nTo reset experiment state, run: python check_experiment_status.py --reset")

if __name__ == '__main__':
    main()
