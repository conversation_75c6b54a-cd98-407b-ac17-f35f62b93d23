# Subprocess和内存管理最佳实践指南

## 📋 概述

本文档详细说明在机器学习实验管理中如何正确使用subprocess和内存管理，特别针对Windows和Unix系统的差异提供具体的实现方案。

## 🎯 设计原则

### 1. 进程隔离的价值
- **内存隔离**: 每个实验独立的内存空间，训练完成后完全释放
- **错误隔离**: 单个实验崩溃不影响整个实验批次
- **状态隔离**: MLflow、CUDA状态等不会在实验间相互影响
- **资源清理**: 进程结束时操作系统强制回收所有资源

### 2. 内存管理挑战
- **大数据集**: Criteo数据集占用1.6GB+内存
- **PyTorch内存泄漏**: GPU内存可能无法完全释放
- **累积效应**: 多个实验可能导致内存累积
- **系统稳定性**: 内存不足可能导致系统崩溃

## 🖥️ Windows系统实现

### 方案1: 改进的Subprocess调用

```python
import subprocess
import sys
import os
import logging
import time
from pathlib import Path

def run_experiment_subprocess_windows(model_type, learning_rate, epochs, batch_size=None):
    """
    Windows系统下的subprocess实验调用
    
    Args:
        model_type: 模型类型 (mlp, dcnv1, dlrm, dcnv2)
        learning_rate: 学习率
        epochs: 训练轮数
        batch_size: 批次大小
    
    Returns:
        bool: 实验是否成功
    """
    # 1. 构建命令
    python_exe = sys.executable
    script_path = os.path.join('src', 'train.py')
    
    cmd = [
        python_exe,
        script_path,
        '--model_type', model_type,
        '--learning_rate', str(learning_rate),
        '--epochs', str(epochs)
    ]
    
    if batch_size:
        cmd.extend(['--batch_size', str(batch_size)])
    
    # 2. 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = os.getcwd()  # 确保Python路径正确
    env['CUDA_VISIBLE_DEVICES'] = '0'  # 限制GPU使用
    
    # 3. Windows特定的subprocess配置
    startupinfo = None
    if os.name == 'nt':  # Windows
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        startupinfo.wShowWindow = subprocess.SW_HIDE  # 隐藏窗口
    
    try:
        logging.info(f"Starting {model_type.upper()} experiment...")
        logging.info(f"Command: {' '.join(cmd)}")
        
        start_time = time.time()
        
        # 4. 执行subprocess
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600,  # 1小时超时
            cwd=os.getcwd(),  # 明确指定工作目录
            env=env,
            startupinfo=startupinfo,  # Windows特定配置
            shell=False,  # Windows上建议False
            check=False  # 不自动抛出异常
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 5. 处理结果
        if result.returncode == 0:
            logging.info(f"✅ {model_type.upper()} experiment completed successfully in {duration:.1f}s")
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)
            return True
        else:
            logging.error(f"❌ {model_type.upper()} experiment failed with return code {result.returncode}")
            if result.stderr:
                logging.error("STDERR:")
                logging.error(result.stderr)
            if result.stdout:
                logging.info("STDOUT:")
                logging.info(result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        logging.error(f"❌ {model_type.upper()} experiment timed out after 1 hour")
        return False
    except FileNotFoundError:
        logging.error(f"❌ Python executable or script not found: {python_exe}, {script_path}")
        return False
    except Exception as e:
        logging.error(f"❌ Unexpected error running {model_type.upper()} experiment: {e}")
        return False

def run_batch_experiments_windows():
    """
    Windows系统下的批量实验执行
    """
    experiments = [
        {'model_type': 'mlp', 'learning_rate': 1.2e-3, 'epochs': 30},
        {'model_type': 'mlp', 'learning_rate': 1.5e-3, 'epochs': 30},
        {'model_type': 'dcnv1', 'learning_rate': 1.5e-3, 'epochs': 30},
        {'model_type': 'dcnv1', 'learning_rate': 2.0e-3, 'epochs': 30},
        {'model_type': 'dlrm', 'learning_rate': 8e-4, 'epochs': 30},
        {'model_type': 'dlrm', 'learning_rate': 1.2e-3, 'epochs': 30},
    ]
    
    successful_experiments = 0
    total_experiments = len(experiments)
    
    for i, exp in enumerate(experiments, 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Experiment {i}/{total_experiments}: {exp['model_type'].upper()}")
        logging.info(f"Learning Rate: {exp['learning_rate']:.2e}")
        logging.info(f"{'='*60}")
        
        success = run_experiment_subprocess_windows(**exp)
        
        if success:
            successful_experiments += 1
            logging.info(f"✅ Experiment {i} completed successfully")
        else:
            logging.error(f"❌ Experiment {i} failed")
        
        # 实验间休息，让系统稳定
        if i < total_experiments:
            logging.info("Waiting 15 seconds before next experiment...")
            time.sleep(15)
    
    # 总结
    logging.info(f"\n{'='*60}")
    logging.info(f"EXPERIMENT SUMMARY")
    logging.info(f"Successful: {successful_experiments}/{total_experiments}")
    logging.info(f"Success Rate: {successful_experiments/total_experiments*100:.1f}%")
    logging.info(f"{'='*60}")
    
    return successful_experiments == total_experiments
```

### 方案2: 直接函数调用 + 强化内存管理

```python
import gc
import torch
import psutil
import logging
from typing import Optional

def run_experiment_direct_windows(model_type, learning_rate, epochs, batch_size=None):
    """
    Windows系统下的直接函数调用实验
    包含强化的内存管理
    """
    try:
        # 1. 记录初始内存状态
        initial_memory = psutil.virtual_memory().percent
        if torch.cuda.is_available():
            initial_gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
        
        logging.info(f"Starting {model_type.upper()} experiment...")
        logging.info(f"Initial CPU memory: {initial_memory:.1f}%")
        if torch.cuda.is_available():
            logging.info(f"Initial GPU memory: {initial_gpu_memory:.2f} GB")
        
        # 2. 强制清理内存
        force_cleanup_memory()
        
        # 3. 导入训练函数（延迟导入避免内存占用）
        from src.train import run_training
        import argparse
        
        # 4. 创建参数对象
        args = argparse.Namespace()
        args.model_type = model_type
        args.learning_rate = learning_rate
        args.epochs = epochs
        args.batch_size = batch_size or 2048
        args.data_dir = 'processed_data'
        
        # 5. 执行训练
        start_time = time.time()
        success = run_training(args)
        end_time = time.time()
        
        # 6. 强制清理内存
        force_cleanup_memory()
        
        # 7. 记录最终内存状态
        final_memory = psutil.virtual_memory().percent
        if torch.cuda.is_available():
            final_gpu_memory = torch.cuda.memory_allocated() / 1024**3
        
        duration = end_time - start_time
        logging.info(f"Experiment completed in {duration:.1f}s")
        logging.info(f"Final CPU memory: {final_memory:.1f}%")
        if torch.cuda.is_available():
            logging.info(f"Final GPU memory: {final_gpu_memory:.2f} GB")
        
        return success
        
    except Exception as e:
        logging.error(f"❌ Error in {model_type.upper()} experiment: {e}")
        # 确保异常情况下也清理内存
        force_cleanup_memory()
        return False

def force_cleanup_memory():
    """
    强制清理内存的工具函数
    """
    try:
        # 1. Python垃圾回收
        gc.collect()
        
        # 2. PyTorch GPU内存清理
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # 3. 强制垃圾回收（多次）
        for _ in range(3):
            gc.collect()
        
        logging.debug("Memory cleanup completed")
        
    except Exception as e:
        logging.warning(f"Memory cleanup failed: {e}")
```

## 🐧 Unix系统实现

### 方案1: Unix优化的Subprocess调用

```python
import subprocess
import sys
import os
import signal
import logging
import time
import resource

def run_experiment_subprocess_unix(model_type, learning_rate, epochs, batch_size=None):
    """
    Unix系统下的subprocess实验调用
    利用Unix的进程管理优势
    """
    # 1. 构建命令
    cmd = [
        sys.executable,
        'src/train.py',
        '--model_type', model_type,
        '--learning_rate', str(learning_rate),
        '--epochs', str(epochs)
    ]
    
    if batch_size:
        cmd.extend(['--batch_size', str(batch_size)])
    
    # 2. 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = os.getcwd()
    env['CUDA_VISIBLE_DEVICES'] = '0'
    env['OMP_NUM_THREADS'] = '8'  # 限制OpenMP线程数
    
    # 3. 设置资源限制
    def set_resource_limits():
        # 限制内存使用（例如：8GB）
        resource.setrlimit(resource.RLIMIT_AS, (8 * 1024**3, 8 * 1024**3))
        # 限制CPU时间（例如：2小时）
        resource.setrlimit(resource.RLIMIT_CPU, (7200, 7200))
    
    try:
        logging.info(f"Starting {model_type.upper()} experiment...")
        logging.info(f"Command: {' '.join(cmd)}")
        
        start_time = time.time()
        
        # 4. 执行subprocess with resource limits
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd(),
            env=env,
            preexec_fn=set_resource_limits,  # Unix特有：设置资源限制
            start_new_session=True  # 创建新的进程组
        )
        
        # 5. 等待完成或超时
        try:
            stdout, stderr = process.communicate(timeout=3600)  # 1小时超时
            returncode = process.returncode
        except subprocess.TimeoutExpired:
            # 超时处理：杀死整个进程组
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            time.sleep(5)
            if process.poll() is None:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
            stdout, stderr = process.communicate()
            logging.error(f"❌ {model_type.upper()} experiment timed out")
            return False
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 6. 处理结果
        if returncode == 0:
            logging.info(f"✅ {model_type.upper()} experiment completed successfully in {duration:.1f}s")
            if stdout:
                logging.info("STDOUT:")
                logging.info(stdout)
            return True
        else:
            logging.error(f"❌ {model_type.upper()} experiment failed with return code {returncode}")
            if stderr:
                logging.error("STDERR:")
                logging.error(stderr)
            return False
            
    except Exception as e:
        logging.error(f"❌ Unexpected error running {model_type.upper()} experiment: {e}")
        return False

def run_batch_experiments_unix():
    """
    Unix系统下的批量实验执行
    利用Unix的进程管理和信号处理
    """
    experiments = [
        {'model_type': 'mlp', 'learning_rate': 1.2e-3, 'epochs': 30},
        {'model_type': 'mlp', 'learning_rate': 1.5e-3, 'epochs': 30},
        {'model_type': 'dcnv1', 'learning_rate': 1.5e-3, 'epochs': 30},
        {'model_type': 'dcnv1', 'learning_rate': 2.0e-3, 'epochs': 30},
        {'model_type': 'dlrm', 'learning_rate': 8e-4, 'epochs': 30},
        {'model_type': 'dlrm', 'learning_rate': 1.2e-3, 'epochs': 30},
    ]
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        logging.info("Received interrupt signal, cleaning up...")
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    successful_experiments = 0
    total_experiments = len(experiments)
    
    for i, exp in enumerate(experiments, 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Experiment {i}/{total_experiments}: {exp['model_type'].upper()}")
        logging.info(f"Learning Rate: {exp['learning_rate']:.2e}")
        logging.info(f"PID: {os.getpid()}")
        logging.info(f"{'='*60}")
        
        success = run_experiment_subprocess_unix(**exp)
        
        if success:
            successful_experiments += 1
            logging.info(f"✅ Experiment {i} completed successfully")
        else:
            logging.error(f"❌ Experiment {i} failed")
        
        # 实验间休息
        if i < total_experiments:
            logging.info("Waiting 10 seconds before next experiment...")
            time.sleep(10)
    
    return successful_experiments == total_experiments
```

### 方案2: Unix内存映射优化

```python
import mmap
import numpy as np
from contextlib import contextmanager

@contextmanager
def memory_mapped_dataset(file_path):
    """
    Unix系统下的内存映射数据集管理
    """
    try:
        # 使用内存映射减少内存占用
        data = np.load(file_path, mmap_mode='r')
        yield data
    finally:
        # 确保内存映射文件正确关闭
        if hasattr(data, '_mmap'):
            data._mmap.close()
        del data

def run_experiment_mmap_unix(model_type, learning_rate, epochs):
    """
    Unix系统下使用内存映射的实验执行
    """
    try:
        logging.info(f"Starting {model_type.upper()} experiment with memory mapping...")

        # 使用内存映射加载数据
        with memory_mapped_dataset('processed_data/train_features.npy') as train_features, \
             memory_mapped_dataset('processed_data/train_labels.npy') as train_labels:

            # 执行训练逻辑
            success = run_training_with_mmap(train_features, train_labels, model_type, learning_rate, epochs)

        return success

    except Exception as e:
        logging.error(f"❌ Memory mapped experiment failed: {e}")
        return False
```

## 🔄 跨平台统一接口

### 自适应实验管理器

```python
import platform
import os
import logging

class ExperimentManager:
    """
    跨平台的实验管理器
    根据操作系统自动选择最佳实现
    """

    def __init__(self):
        self.system = platform.system().lower()
        self.is_windows = self.system == 'windows'
        self.is_unix = self.system in ['linux', 'darwin']

        logging.info(f"Detected system: {self.system}")
        logging.info(f"Using {'Windows' if self.is_windows else 'Unix'} optimizations")

    def run_single_experiment(self, model_type, learning_rate, epochs, batch_size=None, method='auto'):
        """
        运行单个实验，自动选择最佳方法

        Args:
            model_type: 模型类型
            learning_rate: 学习率
            epochs: 训练轮数
            batch_size: 批次大小
            method: 执行方法 ('auto', 'subprocess', 'direct')
        """
        if method == 'auto':
            # 自动选择最佳方法
            if self.is_windows:
                method = 'direct'  # Windows上直接调用更稳定
            else:
                method = 'subprocess'  # Unix上subprocess更好

        if method == 'subprocess':
            if self.is_windows:
                return run_experiment_subprocess_windows(model_type, learning_rate, epochs, batch_size)
            else:
                return run_experiment_subprocess_unix(model_type, learning_rate, epochs, batch_size)
        elif method == 'direct':
            if self.is_windows:
                return run_experiment_direct_windows(model_type, learning_rate, epochs, batch_size)
            else:
                return run_experiment_direct_unix(model_type, learning_rate, epochs, batch_size)
        else:
            raise ValueError(f"Unknown method: {method}")

    def run_batch_experiments(self, experiments=None, method='auto'):
        """
        运行批量实验
        """
        if experiments is None:
            experiments = self.get_default_experiments()

        logging.info(f"Starting batch experiments with method: {method}")
        logging.info(f"Total experiments: {len(experiments)}")

        successful = 0
        for i, exp in enumerate(experiments, 1):
            logging.info(f"\n--- Experiment {i}/{len(experiments)} ---")

            success = self.run_single_experiment(method=method, **exp)

            if success:
                successful += 1
                logging.info(f"✅ Experiment {i} completed")
            else:
                logging.error(f"❌ Experiment {i} failed")

            # 系统特定的休息时间
            if i < len(experiments):
                sleep_time = 15 if self.is_windows else 10
                logging.info(f"Waiting {sleep_time} seconds...")
                time.sleep(sleep_time)

        success_rate = successful / len(experiments) * 100
        logging.info(f"\nBatch experiments completed: {successful}/{len(experiments)} ({success_rate:.1f}%)")

        return successful == len(experiments)

    def get_default_experiments(self):
        """
        获取默认的实验配置
        """
        return [
            {'model_type': 'mlp', 'learning_rate': 1.2e-3, 'epochs': 30},
            {'model_type': 'mlp', 'learning_rate': 1.5e-3, 'epochs': 30},
            {'model_type': 'dcnv1', 'learning_rate': 1.5e-3, 'epochs': 30},
            {'model_type': 'dcnv1', 'learning_rate': 2.0e-3, 'epochs': 30},
            {'model_type': 'dlrm', 'learning_rate': 8e-4, 'epochs': 30},
            {'model_type': 'dlrm', 'learning_rate': 1.2e-3, 'epochs': 30},
        ]

# 使用示例
def main():
    """
    主函数示例
    """
    manager = ExperimentManager()

    # 运行单个实验
    success = manager.run_single_experiment(
        model_type='mlp',
        learning_rate=1.2e-3,
        epochs=30
    )

    # 运行批量实验
    batch_success = manager.run_batch_experiments()

    return success and batch_success

if __name__ == '__main__':
    main()
```

## 📊 内存监控工具

### 实时内存监控

```python
import psutil
import threading
import time
import matplotlib.pyplot as plt
from collections import deque

class MemoryMonitor:
    """
    实时内存监控工具
    """

    def __init__(self, max_points=1000):
        self.max_points = max_points
        self.cpu_memory = deque(maxlen=max_points)
        self.gpu_memory = deque(maxlen=max_points)
        self.timestamps = deque(maxlen=max_points)
        self.monitoring = False
        self.monitor_thread = None

    def start_monitoring(self, interval=1.0):
        """
        开始监控内存使用
        """
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, args=(interval,))
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logging.info("Memory monitoring started")

    def stop_monitoring(self):
        """
        停止监控
        """
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logging.info("Memory monitoring stopped")

    def _monitor_loop(self, interval):
        """
        监控循环
        """
        while self.monitoring:
            try:
                # CPU内存
                cpu_percent = psutil.virtual_memory().percent
                self.cpu_memory.append(cpu_percent)

                # GPU内存
                if torch.cuda.is_available():
                    gpu_used = torch.cuda.memory_allocated() / 1024**3  # GB
                    self.gpu_memory.append(gpu_used)
                else:
                    self.gpu_memory.append(0)

                # 时间戳
                self.timestamps.append(time.time())

                time.sleep(interval)

            except Exception as e:
                logging.warning(f"Memory monitoring error: {e}")

    def save_plot(self, filename='memory_usage.png'):
        """
        保存内存使用图表
        """
        if not self.timestamps:
            logging.warning("No monitoring data to plot")
            return

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

        # CPU内存
        ax1.plot(list(self.timestamps), list(self.cpu_memory), 'b-', label='CPU Memory %')
        ax1.set_ylabel('CPU Memory (%)')
        ax1.set_title('Memory Usage During Experiments')
        ax1.grid(True)
        ax1.legend()

        # GPU内存
        ax2.plot(list(self.timestamps), list(self.gpu_memory), 'r-', label='GPU Memory (GB)')
        ax2.set_ylabel('GPU Memory (GB)')
        ax2.set_xlabel('Time')
        ax2.grid(True)
        ax2.legend()

        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()

        logging.info(f"Memory usage plot saved to {filename}")

# 使用示例
def run_experiment_with_monitoring(model_type, learning_rate, epochs):
    """
    带内存监控的实验执行
    """
    monitor = MemoryMonitor()

    try:
        # 开始监控
        monitor.start_monitoring(interval=5.0)  # 每5秒记录一次

        # 执行实验
        success = run_single_experiment(model_type, learning_rate, epochs)

        return success

    finally:
        # 停止监控并保存图表
        monitor.stop_monitoring()
        monitor.save_plot(f'memory_{model_type}_{int(time.time())}.png')
```

## 🛠️ 故障排除指南

### Windows常见问题

1. **Subprocess创建失败**
   ```python
   # 问题：权限或路径问题
   # 解决：使用绝对路径和正确的startupinfo
   startupinfo = subprocess.STARTUPINFO()
   startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
   startupinfo.wShowWindow = subprocess.SW_HIDE

   # 确保使用正确的Python路径
   python_exe = os.path.abspath(sys.executable)
   script_path = os.path.abspath('src/train.py')
   ```

2. **内存不足错误**
   ```python
   # 问题：Windows内存管理较严格
   # 解决：增加强制清理和休息时间
   def windows_memory_cleanup():
       gc.collect()
       if torch.cuda.is_available():
           torch.cuda.empty_cache()
           torch.cuda.synchronize()
       time.sleep(5)  # Windows需要更长时间
       gc.collect()  # 再次清理
   ```

3. **CUDA内存错误**
   ```python
   # 问题：GPU内存没有完全释放
   # 解决：多次清理和重置
   def reset_cuda_memory():
       if torch.cuda.is_available():
           torch.cuda.empty_cache()
           torch.cuda.synchronize()
           torch.cuda.reset_peak_memory_stats()
           time.sleep(3)
   ```
