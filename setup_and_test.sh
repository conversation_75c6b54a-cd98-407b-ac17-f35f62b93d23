#!/bin/bash
# Linux/Mac shell脚本 - 自动设置环境并运行测试

echo "========================================"
echo "智能推荐系统 - Linux/Mac 自动设置脚本"
echo "========================================"

# 检查Python是否安装
if ! command -v python &> /dev/null; then
    echo "错误: 未找到Python，请先安装Python 3.8+"
    exit 1
fi

echo "Python版本: $(python --version)"

echo "1. 创建虚拟环境..."
python -m venv recommendation_venv
if [ $? -ne 0 ]; then
    echo "错误: 创建虚拟环境失败"
    exit 1
fi

echo "2. 激活虚拟环境..."
source recommendation_venv/bin/activate

echo "3. 安装依赖..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "错误: 安装依赖失败"
    exit 1
fi

echo "4. 运行快速测试..."
python quick_test.py
if [ $? -ne 0 ]; then
    echo "错误: 快速测试失败"
    exit 1
fi

echo "========================================"
echo "🎉 设置完成！系统已准备就绪"
echo "========================================"
echo ""
echo "下一步操作："
echo "1. 运行完整测试: python scripts/run_complete_test.py"
echo "2. 使用您的数据: python examples/setup_data_config.py"
echo "3. 手动训练模型: python src/train.py --model_type mlp --epochs 10"
echo ""
echo "记得先激活虚拟环境: source recommendation_venv/bin/activate"
