#!/usr/bin/env python3
"""
S3 Mock完整端到端测试脚本
使用moto库模拟S3环境，测试完整的数据处理和模型训练流程
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import shutil
import json
import time
from datetime import datetime

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_dir = os.path.join(project_root, 'src')
sys.path.insert(0, src_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    import boto3
    import moto
    from moto import mock_aws
    import s3fs
    MOCK_AVAILABLE = True
    logging.info("S3 mock dependencies loaded successfully")
except ImportError as e:
    MOCK_AVAILABLE = False
    logging.error(f"Mock dependencies not available: {str(e)}")
    logging.error("Install with: pip install moto boto3 s3fs")

class S3MockE2ETester:
    """
    S3 Mock端到端测试器 - 完整测试数据处理和模型训练流程
    """

    def __init__(self):
        self.bucket_name = "test-recommendation-bucket"
        self.s3_client = None
        self.s3_fs = None
        self.temp_dir = None
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.original_config = {}

        # 确保输出目录存在
        self.ensure_output_dirs()

    def ensure_output_dirs(self):
        """
        确保输出目录存在
        """
        dirs = ['processed_data', 'model_outputs', 'data_analysis', 'logs']
        for dir_name in dirs:
            dir_path = os.path.join(self.project_root, dir_name)
            os.makedirs(dir_path, exist_ok=True)

    def setup_mock_s3(self):
        """
        设置Mock S3环境
        """
        if not MOCK_AVAILABLE:
            raise ImportError("Mock dependencies not available")

        logging.info("Setting up mock S3 environment...")

        # 创建S3客户端
        self.s3_client = boto3.client(
            's3',
            region_name='us-east-1',
            aws_access_key_id='testing',
            aws_secret_access_key='testing'
        )

        # 创建测试bucket
        self.s3_client.create_bucket(Bucket=self.bucket_name)

        # 创建s3fs文件系统
        self.s3_fs = s3fs.S3FileSystem(
            key='testing',
            secret='testing',
            client_kwargs={'region_name': 'us-east-1'}
        )

        logging.info(f"Mock S3 bucket '{self.bucket_name}' created successfully")
    
    def generate_realistic_test_data(self):
        """
        生成更真实的测试数据并上传到Mock S3
        """
        logging.info("Generating realistic test data...")

        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()

        # 生成测试数据
        datasets = {
            'train': {'num_files': 4, 'rows_per_file': 2500},  # 10K total
            'validation': {'num_files': 2, 'rows_per_file': 2500},  # 5K total
            'test': {'num_files': 2, 'rows_per_file': 2500}  # 5K total
        }

        for dataset, config in datasets.items():
            dataset_dir = os.path.join(self.temp_dir, dataset)
            os.makedirs(dataset_dir, exist_ok=True)

            for i in range(config['num_files']):
                num_rows = config['rows_per_file']

                # 生成更真实的特征数据
                data = {
                    # 用户特征
                    'user_age': np.random.normal(35, 12, num_rows).clip(18, 80).astype(int),
                    'user_gender': np.random.choice([0, 1], num_rows),
                    'user_city_level': np.random.choice([1, 2, 3, 4], num_rows, p=[0.1, 0.2, 0.4, 0.3]),

                    # 广告特征
                    'ad_category': np.random.randint(0, 50, num_rows),
                    'ad_price': np.random.lognormal(mean=5, sigma=1, size=num_rows).clip(1, 10000),
                    'ad_ctr': np.random.beta(2, 8, num_rows),

                    # 商户特征
                    'merchant_rating': np.random.normal(4.2, 0.8, num_rows).clip(1, 5),
                    'merchant_sales_volume': np.random.lognormal(mean=8, sigma=1.5, size=num_rows),

                    # 上下文特征
                    'hour_of_day': np.random.randint(0, 24, num_rows),
                    'day_of_week': np.random.randint(0, 7, num_rows),

                    # 嵌入向量
                    'ad_embedding': [np.random.normal(0, 1, 128).tolist() for _ in range(num_rows)],
                    'merchant_embedding': [np.random.normal(0, 1, 64).tolist() for _ in range(num_rows)],

                    # 🎯 标签 - 使用offer_click_label
                    'offer_click_label': np.random.binomial(1, 0.15, num_rows)  # 15%点击率
                }

                df = pd.DataFrame(data)

                # 保存为Parquet文件
                file_path = os.path.join(dataset_dir, f"part-{i:05d}.parquet")
                df.to_parquet(file_path, index=False)

                # 上传到Mock S3 - 使用与配置匹配的路径
                s3_key = f"{dataset}/part-{i:05d}.parquet"
                self.s3_client.upload_file(file_path, self.bucket_name, s3_key)

                logging.info(f"Uploaded {s3_key} to mock S3 ({num_rows} rows)")

        logging.info("Realistic test data generation and upload completed")
    
    def setup_mock_config(self):
        """
        设置Mock S3配置
        """
        logging.info("Setting up mock S3 configuration...")

        try:
            import config

            # 备份原始配置
            self.original_config = {
                'TRAIN_DATA_DIR': config.TRAIN_DATA_DIR,
                'VALIDATION_DATA_DIR': config.VALIDATION_DATA_DIR,
                'TEST_DATA_DIR': config.TEST_DATA_DIR,
                'S3_CONFIG': config.S3_CONFIG.copy(),
                'USE_S3': config.USE_S3
            }

            # 设置Mock S3配置 - 路径要与上传路径匹配
            config.TRAIN_DATA_DIR = f"s3://{self.bucket_name}/train/"
            config.VALIDATION_DATA_DIR = f"s3://{self.bucket_name}/validation/"
            config.TEST_DATA_DIR = f"s3://{self.bucket_name}/test/"
            config.S3_CONFIG = {
                'key': 'testing',
                'secret': 'testing',
                'use_ssl': True,
                'client_kwargs': {'region_name': 'us-east-1'}
            }
            config.USE_S3 = True

            logging.info("Mock S3 configuration set successfully")
            return True

        except Exception as e:
            logging.error(f"Error setting up mock config: {str(e)}")
            return False

    def restore_original_config(self):
        """
        恢复原始配置
        """
        if not self.original_config:
            return

        try:
            import config

            config.TRAIN_DATA_DIR = self.original_config['TRAIN_DATA_DIR']
            config.VALIDATION_DATA_DIR = self.original_config['VALIDATION_DATA_DIR']
            config.TEST_DATA_DIR = self.original_config['TEST_DATA_DIR']
            config.S3_CONFIG = self.original_config['S3_CONFIG']
            config.USE_S3 = self.original_config['USE_S3']

            logging.info("Original configuration restored")

        except Exception as e:
            logging.error(f"Error restoring original config: {str(e)}")

    def test_s3_read(self):
        """
        测试S3读取功能
        """
        logging.info("Testing S3 read functionality...")

        try:
            # 使用boto3客户端列出文件
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix='train/'
            )

            if 'Contents' not in response:
                logging.error("No files found in mock S3")
                return False

            files = [obj['Key'] for obj in response['Contents'] if obj['Key'].endswith('.parquet')]
            logging.info(f"Found {len(files)} parquet files in train/")

            if not files:
                logging.error("No parquet files found in mock S3")
                return False

            # 测试读取第一个文件
            first_file_key = files[0]
            logging.info(f"Testing read of {first_file_key}")

            # 使用boto3下载文件到内存并读取
            import io
            obj = self.s3_client.get_object(Bucket=self.bucket_name, Key=first_file_key)
            df = pd.read_parquet(io.BytesIO(obj['Body'].read()))

            logging.info(f"Successfully read {len(df)} rows from {first_file_key}")
            logging.info(f"Columns: {list(df.columns)}")

            # 验证数组列 (Parquet会将list转换为numpy.ndarray)
            ad_emb = df['ad_embedding'].iloc[0]
            merchant_emb = df['merchant_embedding'].iloc[0]

            if isinstance(ad_emb, (list, np.ndarray)) and isinstance(merchant_emb, (list, np.ndarray)):
                logging.info("✓ Array columns preserved correctly")
                logging.info(f"✓ ad_embedding type: {type(ad_emb)}, length: {len(ad_emb)}")
                logging.info(f"✓ merchant_embedding type: {type(merchant_emb)}, length: {len(merchant_emb)}")
            else:
                logging.error(f"✗ Array columns not preserved. ad_emb type: {type(ad_emb)}, merchant_emb type: {type(merchant_emb)}")
                return False

            return True

        except Exception as e:
            logging.error(f"Error testing S3 read: {str(e)}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def test_data_analyzer_with_s3(self):
        """
        测试数据分析器的S3功能
        """
        logging.info("Testing data analyzer with S3...")

        try:
            from data_analyzer import DataAnalyzer

            # 创建数据分析器
            analyzer = DataAnalyzer()

            # 运行数据分析
            results = analyzer.analyze_all_datasets()

            if not results:
                logging.error("Data analyzer failed to analyze datasets")
                return False

            logging.info("✓ Data analyzer completed successfully")
            logging.info(f"✓ Found datasets: {list(results.get('datasets', {}).keys())}")

            # 验证分析结果
            for dataset_name, dataset_info in results.get('datasets', {}).items():
                logging.info(f"✓ {dataset_name}: {dataset_info.get('estimated_total_rows', 0)} rows")
                array_cols = dataset_info.get('array_columns', [])
                logging.info(f"✓ {dataset_name} array columns: {array_cols}")

            return True

        except Exception as e:
            logging.error(f"Error testing data analyzer with S3: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def test_preprocessing_with_s3(self):
        """
        测试预处理器的S3功能
        """
        logging.info("Testing preprocessing with S3...")

        try:
            import subprocess
            import sys

            # 运行预处理脚本
            logging.info("Running preprocessing script...")
            result = subprocess.run([
                sys.executable,
                os.path.join(self.project_root, 'src', 'preprocess.py')
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode != 0:
                logging.error(f"Preprocessing failed: {result.stderr}")
                return False

            logging.info("✓ Preprocessing completed successfully")

            # 验证预处理结果
            processed_dir = os.path.join(self.project_root, 'processed_data')
            expected_files = ['train_features.npy', 'train_labels.npy',
                            'validation_features.npy', 'validation_labels.npy',
                            'test_features.npy', 'test_labels.npy']

            for file_name in expected_files:
                file_path = os.path.join(processed_dir, file_name)
                if os.path.exists(file_path):
                    data = np.load(file_path)
                    logging.info(f"✓ {file_name}: shape = {data.shape}")
                else:
                    logging.error(f"✗ {file_name} not found")
                    return False

            return True

        except Exception as e:
            logging.error(f"Error testing preprocessing with S3: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def test_model_training(self):
        """
        测试模型训练
        """
        logging.info("Testing model training...")

        try:
            import subprocess
            import sys

            # 测试MLP模型训练
            logging.info("Training MLP model...")
            result = subprocess.run([
                sys.executable,
                os.path.join(self.project_root, 'src', 'train.py'),
                '--model_type', 'mlp',
                '--epochs', '2'
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode != 0:
                logging.error(f"MLP training failed: {result.stderr}")
                return False

            logging.info("✓ MLP model training completed")

            # 测试DCNv2模型训练
            logging.info("Training DCNv2 model...")
            result = subprocess.run([
                sys.executable,
                os.path.join(self.project_root, 'src', 'train.py'),
                '--model_type', 'dcnv2',
                '--epochs', '2'
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode != 0:
                logging.error(f"DCNv2 training failed: {result.stderr}")
                return False

            logging.info("✓ DCNv2 model training completed")

            # 验证模型输出
            model_dir = os.path.join(self.project_root, 'model_outputs')
            model_files = [f for f in os.listdir(model_dir) if f.endswith('.pth')]
            result_files = [f for f in os.listdir(model_dir) if f.endswith('.json')]

            if model_files and result_files:
                logging.info(f"✓ Found {len(model_files)} model files and {len(result_files)} result files")
                return True
            else:
                logging.error("✗ Model files or result files not found")
                return False

        except Exception as e:
            logging.error(f"Error testing model training: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def run_complete_e2e_test(self):
        """
        运行完整的端到端测试
        """
        logging.info("🚀 Starting complete S3 Mock E2E test...")

        try:
            # 1. 设置Mock S3配置
            if not self.setup_mock_config():
                return False

            # 2. 测试S3读取
            if not self.test_s3_read():
                return False

            # 3. 测试数据分析
            if not self.test_data_analyzer_with_s3():
                return False

            # 4. 测试数据预处理
            if not self.test_preprocessing_with_s3():
                return False

            # 5. 测试模型训练
            if not self.test_model_training():
                return False

            logging.info("🎉 Complete S3 Mock E2E test passed!")
            return True

        except Exception as e:
            logging.error(f"Complete E2E test failed: {str(e)}")
            return False

        finally:
            # 恢复原始配置
            self.restore_original_config()

    def cleanup(self):
        """
        清理临时文件
        """
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logging.info("Temporary files cleaned up")

def main():
    """
    主函数 - 运行完整的S3 Mock端到端测试
    """
    if not MOCK_AVAILABLE:
        logging.error("Mock dependencies not available. Please install: pip install moto boto3 s3fs")
        return False

    logging.info("=" * 80)
    logging.info("🚀 Starting Complete S3 Mock End-to-End Test")
    logging.info("=" * 80)
    logging.info("This test will:")
    logging.info("1. Create mock S3 environment")
    logging.info("2. Generate realistic test data and upload to S3")
    logging.info("3. Test data analysis from S3")
    logging.info("4. Test data preprocessing from S3")
    logging.info("5. Test model training with S3 data")
    logging.info("=" * 80)

    tester = S3MockE2ETester()

    try:
        with mock_aws():
            # 设置Mock S3环境
            tester.setup_mock_s3()

            # 生成真实的测试数据
            tester.generate_realistic_test_data()

            # 运行完整的端到端测试
            if not tester.run_complete_e2e_test():
                logging.error("❌ Complete E2E test failed")
                return False

            logging.info("=" * 80)
            logging.info("🎉 Complete S3 Mock End-to-End Test PASSED!")
            logging.info("✅ Your system is ready for cloud deployment!")
            logging.info("=" * 80)

            return True

    except Exception as e:
        logging.error(f"S3 Mock E2E test failed: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return False

    finally:
        tester.cleanup()

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
