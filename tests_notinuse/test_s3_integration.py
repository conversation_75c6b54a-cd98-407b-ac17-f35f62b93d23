#!/usr/bin/env python3
"""
S3集成测试脚本
测试S3功能的集成，使用本地数据模拟S3路径
"""

import os
import sys
import logging
import subprocess
import tempfile
import shutil
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_s3_path_detection():
    """
    测试S3路径检测功能
    """
    logging.info("Testing S3 path detection...")
    
    try:
        from config import is_s3_path
        
        # 测试S3路径
        assert is_s3_path("s3://bucket/path/") == True
        assert is_s3_path("s3://bucket/file.parquet") == True
        
        # 测试本地路径
        assert is_s3_path("/local/path") == False
        assert is_s3_path("C:\\local\\path") == False
        assert is_s3_path("./relative/path") == False
        
        logging.info("✓ S3 path detection works correctly")
        return True
        
    except Exception as e:
        logging.error(f"✗ S3 path detection failed: {str(e)}")
        return False

def test_s3_utils_with_local_data():
    """
    测试S3工具函数使用本地数据
    """
    logging.info("Testing S3 utils with local data...")
    
    try:
        from s3_utils import list_parquet_files, read_parquet_file, path_exists
        
        # 测试本地路径功能
        test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "e2e_test_data", "train")
        if os.path.exists(test_dir):
            # 测试路径存在检查
            exists = path_exists(test_dir)
            logging.info(f"Path exists check: {exists}")
            
            # 测试文件列表
            files = list_parquet_files(test_dir)
            logging.info(f"Found {len(files)} parquet files")
            
            if files:
                # 测试文件读取
                df = read_parquet_file(files[0])
                logging.info(f"Read file with {len(df)} rows and {len(df.columns)} columns")
                
                logging.info("✓ S3 utils work correctly with local data")
                return True
            else:
                logging.warning("No parquet files found for testing")
                return True
        else:
            logging.warning(f"Test directory {test_dir} not found")
            return True
            
    except Exception as e:
        logging.error(f"✗ S3 utils test failed: {str(e)}")
        return False

def test_data_analyzer_s3_support():
    """
    测试数据分析器的S3支持
    """
    logging.info("Testing data analyzer S3 support...")
    
    try:
        from data_analyzer import DataAnalyzer
        
        analyzer = DataAnalyzer()
        
        # 测试本地数据分析（模拟S3功能）
        test_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "e2e_test_data", "train")
        if os.path.exists(test_dir):
            analysis = analyzer.analyze_dataset(test_dir, "test")
            
            if analysis:
                logging.info(f"Analysis successful: {analysis['num_files']} files analyzed")
                logging.info(f"Detected columns: {len(analysis['columns_analysis'])}")
                logging.info("✓ Data analyzer S3 support works")
                return True
            else:
                logging.error("Analysis returned None")
                return False
        else:
            logging.warning(f"Test directory {test_dir} not found")
            return True
            
    except Exception as e:
        logging.error(f"✗ Data analyzer S3 test failed: {str(e)}")
        return False

def test_config_s3_settings():
    """
    测试S3配置设置
    """
    logging.info("Testing S3 configuration...")
    
    try:
        import config
        
        # 检查S3配置是否存在
        assert hasattr(config, 'S3_CONFIG')
        assert hasattr(config, 'USE_S3')
        assert hasattr(config, 'is_s3_path')
        
        logging.info(f"USE_S3: {config.USE_S3}")
        logging.info(f"S3_CONFIG keys: {list(config.S3_CONFIG.keys())}")
        
        logging.info("✓ S3 configuration is properly set up")
        return True
        
    except Exception as e:
        logging.error(f"✗ S3 configuration test failed: {str(e)}")
        return False

def run_command_test(command, description):
    """
    运行命令测试
    """
    logging.info(f"Testing: {description}")
    
    try:
        # 切换到src目录运行命令
        src_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src')
        result = subprocess.run(
            command, 
            shell=True, 
            check=True,
            capture_output=True, 
            text=True,
            timeout=60,
            cwd=src_dir
        )
        
        logging.info(f"✓ {description} completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logging.error(f"✗ {description} failed: {e.stderr}")
        return False
    except subprocess.TimeoutExpired:
        logging.error(f"✗ {description} timed out")
        return False

def main():
    """
    主函数 - 运行S3集成测试
    """
    logging.info("=" * 60)
    logging.info("Starting S3 Integration Test")
    logging.info("=" * 60)
    
    tests = [
        ("S3 Path Detection", test_s3_path_detection),
        ("S3 Utils with Local Data", test_s3_utils_with_local_data),
        ("Data Analyzer S3 Support", test_data_analyzer_s3_support),
        ("S3 Configuration", test_config_s3_settings),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logging.info(f"\n>>> Running: {test_name}")
        if test_func():
            passed += 1
        else:
            logging.error(f"Test failed: {test_name}")
    
    # 测试命令行工具
    logging.info("\n>>> Testing command line tools")
    
    # 测试数据分析器命令
    test_data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "e2e_test_data")
    if os.path.exists(test_data_dir):
        if run_command_test("python data_analyzer.py", "Data analyzer command"):
            passed += 1
        total += 1
    
    logging.info("=" * 60)
    if passed == total:
        logging.info(f"🎉 All {total} S3 integration tests passed!")
        logging.info("=" * 60)
        
        logging.info("S3 Integration Test Summary:")
        logging.info("✓ S3 path detection: PASSED")
        logging.info("✓ S3 utilities: PASSED")
        logging.info("✓ Data analyzer S3 support: PASSED")
        logging.info("✓ S3 configuration: PASSED")
        if total > len(tests):
            logging.info("✓ Command line tools: PASSED")
        
        logging.info("\n🚀 Your system is ready for S3 data sources!")
        logging.info("To use S3 data, simply update the paths in config.py:")
        logging.info("  TRAIN_DATA_DIR = 's3://your-bucket/train/'")
        logging.info("  VALIDATION_DATA_DIR = 's3://your-bucket/validation/'")
        logging.info("  TEST_DATA_DIR = 's3://your-bucket/test/'")
        
        return True
    else:
        logging.error(f"✗ {total - passed} out of {total} tests failed")
        logging.info("=" * 60)
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
