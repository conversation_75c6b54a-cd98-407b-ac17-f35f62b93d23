2025-07-18 21:24:23,935 - INFO - === Starting Model Training ===
2025-07-18 21:24:23,935 - INFO - Training configuration:
2025-07-18 21:24:23,935 - INFO -   Model type: dcnv2
2025-07-18 21:24:23,935 - INFO -   Data directory: C:\Users\<USER>\Downloads\win_project\recommendation\processed_data
2025-07-18 21:24:23,935 - INFO -   Epochs: 2
2025-07-18 21:24:23,935 - INFO -   Batch size: 2048
2025-07-18 21:24:23,935 - INFO -   Learning rate: 0.0002
2025-07-18 21:24:23,951 - INFO - Using existing MLflow experiment: recommendation_models (ID: 926548111598714381)
2025-07-18 21:24:24,061 - INFO - Started MLflow run: dcnv2_20250718_212423 (ID: 46ba26b34b3d4166838747e151b6329a)
2025-07-18 21:24:24,061 - INFO - Loading data from: C:\Users\<USER>\Downloads\win_project\recommendation\processed_data
2025-07-18 21:24:24,061 - INFO - Loading train dataset from 5 chunk files with memory mapping
2025-07-18 21:24:24,086 - INFO - train dataset loaded: 11527618 samples, 21 features
2025-07-18 21:24:24,086 - INFO - Loading validation dataset from 2 chunk files with memory mapping
2025-07-18 21:24:24,093 - INFO - validation dataset loaded: 2470204 samples, 21 features
2025-07-18 21:24:24,093 - INFO - Loading test dataset from 2 chunk files with memory mapping
2025-07-18 21:24:24,098 - INFO - test dataset loaded: 2470205 samples, 21 features
2025-07-18 21:24:24,098 - INFO - Input dimension: 21
2025-07-18 21:24:24,098 - INFO - 🖥️  Windows DataLoader config: conservative settings (workers=0)
2025-07-18 21:24:24,098 - INFO - Training model: dcnv2 on device: cpu
2025-07-18 21:24:24,098 - INFO - Model parameters: 177,556
2025-07-18 21:24:24,128 - INFO - Logged config file: config.json
2025-07-18 21:24:25,095 - INFO - Starting training for 2 epochs (early stopping patience: 10)...
2025-07-18 21:28:04,967 - INFO - Epoch [1/2] (219.9s) - Train Loss: 41075841486167539187712.0000, Val Loss: 40114111402230698475520.0000, Val AUC: 0.5150
2025-07-18 21:28:05,027 - INFO - Model saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\dcnv2_best_epoch_1_20250718_212805.pth
2025-07-18 21:28:05,027 - INFO - Results saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\dcnv2_results_20250718_212805.json
2025-07-18 21:28:05,027 - INFO - New best model! Val AUC: 0.5150
2025-07-18 21:31:37,762 - INFO - Epoch [2/2] (212.7s) - Train Loss: 39239850988164167499776.0000, Val Loss: 38320300887850247782400.0000, Val AUC: 0.5150
2025-07-18 21:31:37,810 - INFO - No improvement. Patience: 1/10
2025-07-18 21:31:37,810 - INFO - Training completed in 432.7s
2025-07-18 21:31:37,810 - INFO - Best validation AUC: 0.5150
2025-07-18 21:31:37,810 - INFO - Best model saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\dcnv2_best_epoch_1_20250718_212805.pth
2025-07-18 21:31:37,810 - INFO - === Evaluating on Test Set ===
2025-07-18 21:31:53,621 - INFO - Test Loss: 40167363813051256012800.0000
2025-07-18 21:31:53,621 - INFO - Test AUC: 0.5150
2025-07-18 21:31:56,840 - INFO - Logged model artifact: dcnv2_best_epoch_1_20250718_212423.pth
2025-07-18 21:31:56,856 - INFO - Test results saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\dcnv2_test_results_20250718_213156.json
