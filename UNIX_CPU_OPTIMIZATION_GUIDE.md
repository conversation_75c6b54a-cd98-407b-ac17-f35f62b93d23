# Unix系统CPU优化指南

## 🎯 问题诊断

您的项目在Unix系统下CPU利用率极低的主要原因：

### 1. **DataLoader Workers设置问题**
- **问题**: `num_workers=0` 导致所有数据加载在主线程进行
- **影响**: 完全无法利用多核CPU进行并行数据加载
- **解决**: 已优化为根据CPU核心数自动设置workers

### 2. **批次大小未优化**
- **问题**: 使用Windows保守的批次大小设置
- **影响**: 无法充分利用Unix系统的内存和计算能力
- **解决**: 已实现自适应批次大小优化

### 3. **线程配置不当**
- **问题**: PyTorch、OpenMP、MKL线程数未针对Unix优化
- **影响**: 底层计算库无法利用多核
- **解决**: 已添加系统级线程优化

## 🚀 已实施的优化方案

### 1. **自适应配置系统**
```python
# 新增Unix专用配置 (src/config.py)
UNIX_PERFORMANCE_CONFIG = {
    'dataloader_workers': min(16, os.cpu_count()),  # 多worker并行加载
    'pin_memory': True,                             # 内存固定优化
    'prefetch_factor': 4,                          # 预取优化
    'persistent_workers': True,                     # 持久化worker
    'batch_size_multiplier': 2,                    # 批次大小倍增
    'max_batch_size': 8192,                        # 最大批次
    'torch_threads': os.cpu_count(),               # PyTorch线程
    'omp_num_threads': os.cpu_count(),             # OpenMP线程
    'mkl_num_threads': os.cpu_count(),             # MKL线程
}
```

### 2. **训练脚本优化**
- **系统检测**: 自动识别Unix/Windows系统
- **动态配置**: 根据系统类型应用最佳配置
- **线程优化**: 自动设置PyTorch、OpenMP、MKL线程数
- **内存优化**: 启用pin_memory和persistent_workers

### 3. **性能监控工具**
- **CPU监控**: `scripts/cpu_performance_monitor.py`
- **系统优化**: `scripts/unix_performance_optimizer.py`
- **效果测试**: `scripts/test_cpu_optimizations.py`

## 📊 使用方法

### 1. **立即应用优化**
```bash
# 直接运行训练（自动应用Unix优化）
python src/train.py --model_type mlp --epochs 3

# 查看系统优化建议
python scripts/unix_performance_optimizer.py

# 应用系统级优化
python scripts/unix_performance_optimizer.py --apply
```

### 2. **性能监控**
```bash
# 监控CPU使用情况（运行5分钟）
python scripts/cpu_performance_monitor.py --duration 300

# 在另一个终端启动训练
python src/train.py --model_type mlp --epochs 1
```

### 3. **效果对比测试**
```bash
# 运行完整的优化效果对比测试
python scripts/test_cpu_optimizations.py

# 快速测试
python scripts/test_cpu_optimizations.py --quick
```

## 🔍 预期改善效果

### CPU利用率提升
- **优化前**: 总CPU使用率 < 30%，大部分核心闲置
- **优化后**: 总CPU使用率 > 70%，多核心并行工作

### 训练速度提升
- **数据加载**: 多worker并行，减少I/O等待
- **计算效率**: 多线程矩阵运算，充分利用CPU
- **内存优化**: pin_memory减少CPU-GPU传输开销

### 具体数值预期
- **CPU利用率**: 提升 50-150%
- **训练速度**: 提升 30-80%
- **多核利用**: 从1-2个核心提升到所有核心

## ⚙️ 配置说明

### 自动优化参数
```python
# 根据系统自动调整的参数
dataloader_workers = min(16, cpu_count)      # 最多16个worker
batch_size = original_size * multiplier      # 批次大小倍增
torch_threads = cpu_count                    # PyTorch线程数
omp_threads = cpu_count                      # OpenMP线程数
```

### 环境变量控制
```bash
# 强制使用保守配置（用于对比测试）
export FORCE_CONSERVATIVE_CONFIG=1

# 手动设置线程数
export OMP_NUM_THREADS=16
export MKL_NUM_THREADS=16
```

## 🧪 验证优化效果

### 1. **运行对比测试**
```bash
# 完整对比测试（推荐）
python scripts/test_cpu_optimizations.py
```

### 2. **手动验证**
```bash
# 终端1: 启动监控
python scripts/cpu_performance_monitor.py --duration 180

# 终端2: 运行训练
python src/train.py --model_type mlp --epochs 1

# 查看CPU使用率是否显著提升
```

### 3. **系统工具验证**
```bash
# 使用htop查看CPU使用情况
htop

# 使用top查看负载
top

# 查看进程线程数
ps -eLf | grep python
```

## 📈 性能调优建议

### 根据系统配置调整

#### 高性能服务器 (16+ cores, 32+ GB RAM)
```python
UNIX_PERFORMANCE_CONFIG.update({
    'dataloader_workers': 16,
    'max_batch_size': 16384,
    'batch_size_multiplier': 3,
    'prefetch_factor': 6,
})
```

#### 中等配置 (8-16 cores, 16-32 GB RAM)
```python
UNIX_PERFORMANCE_CONFIG.update({
    'dataloader_workers': 8,
    'max_batch_size': 8192,
    'batch_size_multiplier': 2,
    'prefetch_factor': 4,
})
```

#### 低配置 (4-8 cores, 8-16 GB RAM)
```python
UNIX_PERFORMANCE_CONFIG.update({
    'dataloader_workers': 4,
    'max_batch_size': 4096,
    'batch_size_multiplier': 1,
    'prefetch_factor': 2,
})
```

## 🔧 故障排除

### 常见问题

#### 1. **CPU使用率仍然很低**
```bash
# 检查是否正确应用了优化
python -c "from src.config import IS_UNIX, UNIX_PERFORMANCE_CONFIG; print(f'Unix: {IS_UNIX}, Workers: {UNIX_PERFORMANCE_CONFIG[\"dataloader_workers\"]}')"

# 检查环境变量
echo $OMP_NUM_THREADS
echo $MKL_NUM_THREADS
```

#### 2. **内存不足错误**
```bash
# 减少批次大小
export FORCE_CONSERVATIVE_CONFIG=1

# 或手动调整配置
# 在src/config.py中减少max_batch_size
```

#### 3. **进程卡死或崩溃**
```bash
# 检查是否有资源限制
ulimit -a

# 减少worker数量
# 在src/config.py中调整dataloader_workers
```

## 📊 监控指标

### 关键性能指标
- **CPU总使用率**: 目标 > 70%
- **多核利用率**: 目标 > 80%的核心被使用
- **负载平均值**: 目标接近CPU核心数
- **内存使用率**: 目标 60-80%
- **训练时间**: 相比优化前减少30%+

### 监控命令
```bash
# 实时CPU监控
watch -n 1 'cat /proc/loadavg; echo "CPU:"; grep "cpu " /proc/stat'

# 内存监控
watch -n 1 'free -h'

# 进程监控
watch -n 1 'ps aux | grep python'
```

## 🎯 总结

通过这些优化，您的Unix系统应该能够：

1. **充分利用多核CPU**: 从单核工作提升到多核并行
2. **显著提升训练速度**: 数据加载和计算并行化
3. **优化内存使用**: 减少CPU-GPU传输开销
4. **自适应配置**: 根据系统自动选择最佳参数

如果优化后仍有问题，请运行诊断脚本并查看详细报告：
```bash
python scripts/test_cpu_optimizations.py
python scripts/unix_performance_optimizer.py --test
```
