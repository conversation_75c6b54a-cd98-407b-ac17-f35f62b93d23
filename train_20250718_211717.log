2025-07-18 21:17:17,448 - INFO - === Starting Model Training ===
2025-07-18 21:17:17,448 - INFO - Training configuration:
2025-07-18 21:17:17,448 - INFO -   Model type: mlp
2025-07-18 21:17:17,448 - INFO -   Data directory: C:\Users\<USER>\Downloads\win_project\recommendation\processed_data
2025-07-18 21:17:17,448 - INFO -   Epochs: 2
2025-07-18 21:17:17,448 - INFO -   Batch size: 2048
2025-07-18 21:17:17,448 - INFO -   Learning rate: 0.0012
2025-07-18 21:17:17,463 - INFO - Using existing MLflow experiment: recommendation_models (ID: 926548111598714381)
2025-07-18 21:17:17,576 - INFO - Started MLflow run: mlp_20250718_211717 (ID: 4009854c42f24b9ea8e6013e2d2d8e73)
2025-07-18 21:17:17,576 - INFO - Loading data from: C:\Users\<USER>\Downloads\win_project\recommendation\processed_data
2025-07-18 21:17:17,576 - INFO - Loading train dataset from 5 chunk files with memory mapping
2025-07-18 21:17:17,602 - INFO - train dataset loaded: 11527618 samples, 21 features
2025-07-18 21:17:17,602 - INFO - Loading validation dataset from 2 chunk files with memory mapping
2025-07-18 21:17:17,607 - INFO - validation dataset loaded: 2470204 samples, 21 features
2025-07-18 21:17:17,607 - INFO - Loading test dataset from 2 chunk files with memory mapping
2025-07-18 21:17:17,614 - INFO - test dataset loaded: 2470205 samples, 21 features
2025-07-18 21:17:17,614 - INFO - Input dimension: 21
2025-07-18 21:17:17,614 - INFO - 🖥️  Windows DataLoader config: conservative settings (workers=0)
2025-07-18 21:17:17,617 - INFO - Training model: mlp on device: cpu
2025-07-18 21:17:17,617 - INFO - Model parameters: 177,409
2025-07-18 21:17:17,648 - INFO - Logged config file: config.json
2025-07-18 21:17:18,814 - INFO - Starting training for 2 epochs (early stopping patience: 8)...
2025-07-18 21:20:39,558 - INFO - Epoch [1/2] (200.7s) - Train Loss: 0.5796, Val Loss: 0.5712, Val AUC: 0.6977
2025-07-18 21:20:39,624 - INFO - Model saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_best_epoch_1_20250718_212039.pth
2025-07-18 21:20:39,624 - INFO - Results saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_results_20250718_212039.json
2025-07-18 21:20:39,624 - INFO - New best model! Val AUC: 0.6977
2025-07-18 21:24:00,707 - INFO - Epoch [2/2] (201.1s) - Train Loss: 0.5711, Val Loss: 0.5677, Val AUC: 0.7042
2025-07-18 21:24:00,747 - INFO - Model saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_best_epoch_2_20250718_212400.pth
2025-07-18 21:24:00,747 - INFO - Results saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_results_20250718_212400.json
2025-07-18 21:24:00,747 - INFO - New best model! Val AUC: 0.7042
2025-07-18 21:24:00,747 - INFO - Training completed in 401.9s
2025-07-18 21:24:00,747 - INFO - Best validation AUC: 0.7042
2025-07-18 21:24:00,747 - INFO - Best model saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_best_epoch_2_20250718_212400.pth
2025-07-18 21:24:00,747 - INFO - === Evaluating on Test Set ===
2025-07-18 21:24:15,717 - INFO - Test Loss: 0.5673
2025-07-18 21:24:15,729 - INFO - Test AUC: 0.7045
2025-07-18 21:24:20,700 - INFO - Logged model artifact: mlp_best_epoch_2_20250718_211717.pth
2025-07-18 21:24:20,700 - INFO - Test results saved to: C:\Users\<USER>\Downloads\win_project\recommendation\model_outputs\mlp_test_results_20250718_212420.json
